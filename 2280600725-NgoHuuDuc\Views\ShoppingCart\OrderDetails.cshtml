@model NgoHuuDuc_2280600725.Models.Order
@using NgoHuuDuc_2280600725.Models.Enums
@using NgoHuuDuc_2280600725.Extensions

@{
    ViewData["Title"] = "Chi tiết đơn hàng";
}

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4 screen-only">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
        <li class="breadcrumb-item"><a asp-controller="ShoppingCart" asp-action="MyOrders">Đơn hàng của tôi</a></li>
        <li class="breadcrumb-item active" aria-current="page">Chi tiết đơn hàng #@Model.Id.ToString("D6")</li>
    </ol>
</nav>

<!-- Page Header (Hidden when printing) -->
<div class="d-flex justify-content-between align-items-center mb-4 screen-only">
    <h2><i class="fas fa-file-invoice me-2"></i>Chi tiết đơn hàng #@Model.Id.ToString("D6")</h2>
    <div>
        <button type="button" class="btn btn-elegant-primary me-2" onclick="printInvoice()">
            <i class="fas fa-print me-2"></i>In hóa đơn
        </button>
        <a asp-action="MyOrders" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
        </a>
    </div>
</div>

<!-- Professional Invoice Layout (Only visible when printing) -->
<div class="invoice-container print-only">
    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="company-info">
            <div class="company-name">ELEGANT SUITS</div>
            <div class="company-details">
                Địa chỉ: 123 Đường Thời Trang, Quận 1, TP.HCM<br>
                Điện thoại: (028) 1234-5678 | Email: <EMAIL><br>
                Website: www.elegantsuits.vn | MST: 0123456789
            </div>
        </div>
        <h3 style="margin: 15px 0 5px 0; font-size: 20px;">HÓA ĐƠN BÁN HÀNG</h3>
        <div style="font-size: 12px;">Invoice No: #@Model.Id.ToString("D6")</div>
    </div>

    <!-- Invoice Information -->
    <div class="invoice-info">
        <div class="invoice-details">
            <h6>THÔNG TIN HÓA ĐƠN</h6>
            <div><strong>Số hóa đơn:</strong> #@Model.Id.ToString("D6")</div>
            <div><strong>Ngày xuất:</strong> @DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")</div>
            <div><strong>Ngày đặt hàng:</strong> @Model.OrderDate.ToString("dd/MM/yyyy HH:mm:ss")</div>
            <div><strong>Phương thức thanh toán:</strong> Thanh toán khi nhận hàng (COD)</div>
        </div>
        <div class="customer-details">
            <h6>THÔNG TIN KHÁCH HÀNG</h6>
            <div><strong>Họ và tên:</strong> @User.Identity.Name</div>
            <div><strong>Địa chỉ giao hàng:</strong><br>@Model.ShippingAddress</div>
            @if (!string.IsNullOrEmpty(Model.Notes))
            {
                <div><strong>Ghi chú:</strong> @Model.Notes</div>
            }
        </div>
    </div>

    <!-- Product Details Table -->
    <table class="invoice-table">
        <thead>
            <tr>
                <th style="width: 5%;">STT</th>
                <th style="width: 45%;">Tên sản phẩm</th>
                <th style="width: 10%;">Số lượng</th>
                <th style="width: 20%;">Đơn giá</th>
                <th style="width: 20%;">Thành tiền</th>
            </tr>
        </thead>
        <tbody>
            @if (Model.OrderDetails != null)
            {
                @for (int i = 0; i < Model.OrderDetails.Count; i++)
                {
                    var detail = Model.OrderDetails.ElementAt(i);
                    <tr>
                        <td class="text-center">@(i + 1)</td>
                        <td>@(detail.Product?.Name ?? "Sản phẩm #" + detail.ProductId)</td>
                        <td class="text-center">@detail.Quantity</td>
                        <td class="text-right">@detail.Price.ToString("N0") VND</td>
                        <td class="text-right">@((detail.Price * detail.Quantity).ToString("N0")) VND</td>
                    </tr>
                }
            }
        </tbody>
    </table>

    <!-- Order Notes Section -->
    @if (!string.IsNullOrEmpty(Model.Notes))
    {
        <div class="order-notes-section">
            <h6 style="font-size: 14px; font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid #ddd; padding-bottom: 3px;">
                GHI CHÚ ĐƠN HÀNG
            </h6>
            <div style="padding: 10px; border: 1px solid #ccc; background-color: #f9f9f9; margin-bottom: 15px; font-size: 11px; line-height: 1.4;">
                @Model.Notes
            </div>
        </div>
    }

    <!-- Invoice Summary -->
    <div class="invoice-summary">
        <table class="summary-table">
            <tr>
                <td>Tạm tính:</td>
                <td class="text-right">@Model.TotalPrice.ToString("N0") VND</td>
            </tr>
            <tr>
                <td>Phí vận chuyển:</td>
                <td class="text-right">0 VND</td>
            </tr>
            <tr>
                <td>Thuế VAT (0%):</td>
                <td class="text-right">0 VND</td>
            </tr>
            <tr class="total-row">
                <td><strong>TỔNG CỘNG:</strong></td>
                <td class="text-right"><strong>@Model.TotalPrice.ToString("N0") VND</strong></td>
            </tr>
        </table>
    </div>

    <!-- Invoice Footer -->
    <div class="invoice-footer">
        <div style="margin-bottom: 10px;">
            <strong>Cảm ơn quý khách đã mua hàng tại Elegant Suits!</strong>
        </div>
        <div>
            Hotline hỗ trợ: 1900-1234 | Email: <EMAIL><br>
            Chính sách đổi trả trong vòng 7 ngày | Bảo hành chính hãng
        </div>
        <div style="margin-top: 15px; font-style: italic;">
            Hóa đơn được in tự động từ hệ thống - Ngày in: @DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
        </div>
    </div>
</div>

<div class="row screen-only" style="display:flex;">
    <!-- Thông tin đơn hàng -->
    <div class="col-lg-8">
        <div class="card card-elegant shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2 text-primary"></i>Thông tin đơn hàng</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" style="width: 40%;">Mã đơn hàng:</td>
                                <td class="text-primary fw-bold">#@Model.Id.ToString("D6")</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Ngày đặt hàng:</td>
                                <td>@Model.OrderDate.ToString("dd/MM/yyyy HH:mm:ss")</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Trạng thái:</td>
                                <td>
                                    @{
                                        var badgeClass = Model.Status switch
                                        {
                                            OrderStatus.Pending => "bg-warning",
                                            OrderStatus.Confirmed => "bg-info",
                                            OrderStatus.Shipping => "bg-primary",
                                            OrderStatus.Delivered => "bg-success",
                                            OrderStatus.Cancelled => "bg-danger",
                                            OrderStatus.Returned => "bg-secondary",
                                            _ => "bg-light"
                                        };
                                    }
                                    <span class="badge @badgeClass">
                                        <i class="fas fa-clock me-1"></i>
                                        @Model.Status.GetDisplayName()
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" style="width: 40%;">Tổng tiền:</td>
                                <td class="text-success fw-bold fs-5">
                                    @Model.TotalPrice.ToString("C0")
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Số sản phẩm:</td>
                                <td>
                                    @if (Model.OrderDetails != null)
                                    {
                                        @Model.OrderDetails.Sum(d => d.Quantity)
                                    }
                                    else
                                    {
                                        @:0
                                    } sản phẩm
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Phương thức thanh toán:</td>
                                <td>Thanh toán khi nhận hàng (COD)</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    <div class="card card-elegant">
        <div class="card-header">
            <h5 class="mb-0">Sản phẩm đã đặt</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Sản phẩm</th>
                            <th>Kích cỡ</th>
                            <th>Đơn giá</th>
                            <th>Số lượng</th>
                            <th>Thành tiền</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.OrderDetails != null)
                        {
                            @foreach (var item in Model.OrderDetails)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if (item.Product != null && !string.IsNullOrEmpty(item.Product.ImageUrl))
                                            {
                                                <img src="@item.Product.ImageUrl" alt="@item.Product.Name" style="width:50px; height:50px; object-fit:cover" class="me-2" />
                                            }
                                            <span>@(item.Product?.Name ?? "Sản phẩm không còn tồn tại")</span>
                                        </div>
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(item.Size))
                                        {
                                            <span class="badge bg-info">@item.Size</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>@item.Price.ToString("C0")</td>
                                    <td>@item.Quantity</td>
                                    <td>@((item.Price * item.Quantity).ToString("C0"))</td>
                                </tr>
                            }
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="4" class="text-end"><strong>Tổng cộng:</strong></td>
                            <td><strong>@Model.TotalPrice.ToString("C0")</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <!-- Thông tin khách hàng -->
    <div class="col-lg-4">
        <div class="card card-elegant shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-user me-2 text-primary"></i>Thông tin giao hàng</h5>
            </div>
            <div class="card-body p-3">
                <div class="mb-3">
                    <label class="form-label fw-bold text-muted small">
                        <i class="fas fa-user me-1"></i>Khách hàng:
                    </label>
                    <p class="mb-0 fs-6">@User.Identity.Name</p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold text-muted small">
                        <i class="fas fa-map-marker-alt me-1"></i>Địa chỉ giao hàng:
                    </label>
                    <p class="mb-0 fs-6">@Model.ShippingAddress</p>
                </div>
                @if (!string.IsNullOrEmpty(Model.Notes))
                {
                    <div class="mb-3">
                        <label class="form-label fw-bold text-muted small">
                            <i class="fas fa-sticky-note me-1"></i>Ghi chú:
                        </label>
                        <p class="mb-0 fs-6">@Model.Notes</p>
                    </div>
                }
            </div>
        </div>

        <!-- Timeline trạng thái đơn hàng -->
        <div class="card card-elegant shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-clock me-2 text-primary"></i>Trạng thái đơn hàng</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    @{
                        var currentStatus = Model.Status.ToString();
                        var statusOrder = new[] { "Pending", "Confirmed", "Shipping", "Delivered" };
                        var currentIndex = Array.IndexOf(statusOrder, currentStatus);
                        if (currentStatus == "Cancelled") {
                            currentIndex = 0;
                        }
                    }

                    <!-- Đơn hàng đã được đặt -->
                    <div class="timeline-item @(currentIndex >= 0 && currentStatus != "Cancelled" ? "active" : "")">
                        <div class="timeline-marker @(currentIndex >= 0 && currentStatus != "Cancelled" ? "bg-success" : "bg-secondary")"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Đơn hàng đã được đặt</h6>
                            <small class="text-muted">@Model.OrderDate.ToString("dd/MM/yyyy HH:mm")</small>
                        </div>
                    </div>

                    <!-- Xác nhận đơn hàng -->
                    <div class="timeline-item @(currentIndex >= 1 && currentStatus != "Cancelled" ? "active" : "")">
                        <div class="timeline-marker @(currentIndex >= 1 && currentStatus != "Cancelled" ? "bg-success" : (currentStatus == "Cancelled" ? "bg-danger" : "bg-secondary"))"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Xác nhận đơn hàng</h6>
                            <small class="text-muted">@(currentStatus == "Cancelled" ? "Đã hủy" : "Chờ xử lý")</small>
                        </div>
                    </div>

                    <!-- Đóng gói và giao hàng -->
                    <div class="timeline-item @(currentIndex >= 2 && currentStatus != "Cancelled" ? "active" : "")">
                        <div class="timeline-marker @(currentIndex >= 2 && currentStatus != "Cancelled" ? "bg-success" : (currentStatus == "Cancelled" ? "bg-danger" : "bg-secondary"))"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Đóng gói và giao hàng</h6>
                            <small class="text-muted">@(currentStatus == "Cancelled" ? "Đã hủy" : "Chờ xử lý")</small>
                        </div>
                    </div>

                    <!-- Giao hàng thành công -->
                    <div class="timeline-item @(currentIndex >= 3 && currentStatus != "Cancelled" ? "active" : "")">
                        <div class="timeline-marker @(currentIndex >= 3 && currentStatus != "Cancelled" ? "bg-success" : (currentStatus == "Cancelled" ? "bg-danger" : "bg-secondary"))"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Giao hàng thành công</h6>
                            <small class="text-muted">@(currentStatus == "Cancelled" ? "Đã hủy" : "Chờ xử lý")</small>
                        </div>
                    </div>

                    @if (currentStatus == "Cancelled")
                    {
                        <div class="timeline-item active">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Đơn hàng đã bị hủy</h6>
                                <small class="text-muted">Đơn hàng không thể tiếp tục xử lý</small>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for timeline and print functionality -->
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-item.active .timeline-marker {
    background: #28a745 !important;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 14px;
}

.timeline-content small {
    font-size: 12px;
}

/* Hide invoice container on screen, only show when printing */
.print-only {
    display: none !important;
}

/* Show screen content normally, hide when printing */
.screen-only {
    display: block;
}

/* Print Styles for Professional Invoice */
@@media print {
    /* Show invoice container when printing */
    .print-only {
        display: block !important;
    }

    /* Hide screen-only content when printing */
    .screen-only {
        display: none !important;
    }

    /* Hide non-essential elements */
    .btn, .breadcrumb, .navbar, .card-header,
    .timeline, .alert,
    .dropdown, .badge, .text-muted small,
    nav, header, footer, .no-print {
        display: none !important;
    }

    /* Reset page margins and layout */
    @@page {
        margin: 0.5in;
        size: A4;
    }

    body {
        font-family: 'Arial', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: white;
    }

    .container {
        max-width: 100%;
        margin: 0;
        padding: 0;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
        margin-bottom: 0 !important;
        page-break-inside: avoid;
    }

    .card-body {
        padding: 0 !important;
    }

    /* Invoice Header */
    .invoice-header {
        text-align: center;
        border-bottom: 2px solid #000;
        padding-bottom: 15px;
        margin-bottom: 20px;
    }

    .company-info {
        margin-bottom: 10px;
    }

    .company-name {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .company-details {
        font-size: 11px;
        line-height: 1.3;
    }

    /* Invoice Info */
    .invoice-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        border-bottom: 1px solid #ccc;
        padding-bottom: 15px;
    }

    .invoice-details, .customer-details {
        width: 48%;
    }

    .invoice-details h6, .customer-details h6 {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 8px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 3px;
    }

    /* Product Table */
    .invoice-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .invoice-table th,
    .invoice-table td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
        font-size: 11px;
    }

    .invoice-table th {
        background-color: #f0f0f0;
        font-weight: bold;
        text-align: center;
    }

    .invoice-table .text-center {
        text-align: center;
    }

    .invoice-table .text-right {
        text-align: right;
    }

    /* Order Notes */
    .order-notes-section {
        margin-bottom: 15px;
        page-break-inside: avoid;
    }

    /* Summary */
    .invoice-summary {
        float: right;
        width: 300px;
        margin-top: 10px;
    }

    .summary-table {
        width: 100%;
        border-collapse: collapse;
    }

    .summary-table td {
        padding: 5px 10px;
        border: 1px solid #000;
        font-size: 12px;
    }

    .summary-table .total-row {
        font-weight: bold;
        background-color: #f0f0f0;
    }

    /* Footer */
    .invoice-footer {
        clear: both;
        margin-top: 30px;
        padding-top: 15px;
        border-top: 1px solid #ccc;
        text-align: center;
        font-size: 10px;
    }
}
</style>

<!-- JavaScript for print functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced print functionality
    window.printInvoice = function() {
        // Set document title for print
        const originalTitle = document.title;
        document.title = 'Hóa đơn #@Model.Id.ToString("D6") - Elegant Suits';

        // Print the page
        window.print();

        // Restore original title
        document.title = originalTitle;
    };

    // Add keyboard shortcut for printing (Ctrl+P)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printInvoice();
        }
    });
});
</script>