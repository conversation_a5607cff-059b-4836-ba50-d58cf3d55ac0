@model List<CartItem>

<div class="container mt-4">
    <h2 class="mb-4">Giỏ hàng</h2>

    @if (Model.Any())
    {
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Sản phẩm</th>
                                <th>Giá</th>
                                <th>Số lượng</th>
                                <th>Tổng cộng</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="@item.ImageUrl" style="width:50px; height:50px; object-fit:cover" class="me-2" />
                                            <span>@item.ProductName</span>
                                        </div>
                                    </td>
                                    <td>@item.Price.ToString("C")</td>
                                    <td>
                                        <div class="input-group" style="width: 120px;">
                                            <button class="btn btn-outline-secondary btn-sm quantity-btn" data-action="decrease" data-product-id="@item.ProductId">-</button>
                                            <input type="number" class="form-control form-control-sm text-center quantity-input" value="@item.Quantity" min="1" readonly>
                                            <button class="btn btn-outline-secondary btn-sm quantity-btn" data-action="increase" data-product-id="@item.ProductId">+</button>
                                        </div>
                                    </td>
                                    <td class="item-total">@((item.Price * item.Quantity).ToString("C"))</td>
                                    <td>
                                        <form asp-action="RemoveFromCart" method="post" class="d-inline remove-from-cart-form">
                                            <input type="hidden" name="productId" value="@item.ProductId" />
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class='bx bx-trash'></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Tổng cộng:</strong></td>
                                <td><strong>@Model.Sum(i => i.Price * i.Quantity).ToString("C")</strong></td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="text-end mt-3">
                    <a asp-controller="Product" asp-action="Index" class="btn btn-secondary">Tiếp tục mua sắm</a>
                    <a asp-controller="ShoppingCart" asp-action="Checkout" class="btn btn-success">Thanh toán</a>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center">
            <p class="lead">Giỏ hàng của bạn đang trống</p>
            <a asp-controller="Home" asp-action="Index" class="btn btn-primary">Bắt đầu mua sắm</a>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('.remove-from-cart-form').on('submit', function (e) {
                e.preventDefault();
                var form = $(this);

                $.ajax({
                    url: form.attr('action'),
                    method: 'POST',
                    data: form.serialize(),
                    success: function () {
                        location.reload(); // Reload the page after removing item
                        updateCartCount(); // Update cart count
                    },
                    error: function () {
                        alert('Có lỗi xảy ra khi xóa sản phẩm.');
                    }
                });
            });

            $('.quantity-btn').on('click', function () {
                const button = $(this);
                const productId = button.data('product-id');
                const action = button.data('action');
                const inputGroup = button.closest('.input-group');
                const quantityInput = inputGroup.find('.quantity-input');
                let currentQuantity = parseInt(quantityInput.val());

                if (action === 'increase') {
                    currentQuantity++;
                } else if (action === 'decrease' && currentQuantity > 1) {
                    currentQuantity--;
                }

                $.ajax({
                    url: '/Home/UpdateQuantity',
                    method: 'POST',
                    data: {
                        productId: productId,
                        quantity: currentQuantity
                    },
                    success: function (response) {
                        if (response.success) {
                            quantityInput.val(currentQuantity);
                            // Update item total
                            const price = parseFloat(button.closest('tr').find('td:eq(1)').text().replace('$', '').replace(',', ''));
                            const newTotal = (price * currentQuantity).toFixed(2);
                            button.closest('tr').find('.item-total').text('$' + newTotal);

                            // Update cart total
                            let cartTotal = 0;
                            $('.item-total').each(function() {
                                cartTotal += parseFloat($(this).text().replace('$', '').replace(',', ''));
                            });
                            $('tfoot strong').text('$' + cartTotal.toFixed(2));

                            // Update cart badge
                            $('.cart-count').text(response.cartCount);
                        }
                    },
                    error: function () {
                        alert('Có lỗi xảy ra khi cập nhật số lượng.');
                    }
                });
            });
        });
    </script>
}