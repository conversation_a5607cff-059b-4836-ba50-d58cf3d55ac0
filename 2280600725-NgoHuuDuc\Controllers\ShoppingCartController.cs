﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NgoHuuDuc_2280600725.Data;
using NgoHuuDuc_2280600725.Extensions;
using NgoHuuDuc_2280600725.Models;
using NgoHuuDuc_2280600725.Models.Enums;
using System;
using System.Collections.Generic;

namespace NgoHuuDuc_2280600725.Controllers
{
    [Authorize]
    public class ShoppingCartController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public ShoppingCartController(ApplicationDbContext context,
        UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }
        public async Task<IActionResult> Index()
        {
            try
            {
                var userId = User.Identity?.Name;
                if (string.IsNullOrEmpty(userId))
                {
                    return RedirectToAction("Login", "Account");
                }

                var cart = await _context.Carts
                    .Include(c => c.Items)
                    .ThenInclude(i => i.Product)
                    .FirstOrDefaultAsync(c => c.UserId == userId);

                if (cart == null || cart.Items.Count == 0)
                {
                    TempData["ErrorMessage"] = "Giỏ hàng của bạn đang trống. Vui lòng thêm sản phẩm trước khi thanh toán.";
                    return View(new List<CartItem>());
                }

                return View(cart.Items);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ShoppingCart Index: {ex.Message}");
                TempData["ErrorMessage"] = "Đã xảy ra lỗi khi tải giỏ hàng của bạn.";
                return View(new List<CartItem>());
            }
        }

        public async Task<IActionResult> Checkout()
        {
            try
            {
                // Lấy thông tin người dùng hiện tại
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Account");
                }

                Console.WriteLine($"Checkout GET - User: {currentUser.Id} - {currentUser.Email}");

                // Lấy giỏ hàng (Cart sử dụng email làm UserId)
                var cart = await _context.Carts
                    .Include(c => c.Items)
                    .ThenInclude(i => i.Product)
                    .FirstOrDefaultAsync(c => c.UserId == currentUser.Email);

                if (cart == null || cart.Items.Count == 0)
                {
                    TempData["ErrorMessage"] = "Giỏ hàng của bạn đang trống. Vui lòng thêm sản phẩm trước khi thanh toán.";
                    return RedirectToAction("Index", "Home");
                }

                // Pass cart items to view for order summary
                ViewBag.CartItems = cart.Items.ToList();

                var order = new Order();
                return View(order);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in Checkout GET: {ex.Message}");
                TempData["ErrorMessage"] = "Đã xảy ra lỗi. Vui lòng thử lại.";
                return RedirectToAction("Index", "ShoppingCart");
            }
        }

        [HttpPost]
        public async Task<IActionResult> Checkout(Order order)
        {
            try
            {
                Console.WriteLine($"=== CHECKOUT POST START ===");
                Console.WriteLine($"Order received - ShippingAddress: '{order.ShippingAddress}', Notes: '{order.Notes}', PaymentMethod: {order.PaymentMethod} ({(int)order.PaymentMethod})");
                Console.WriteLine($"ModelState.IsValid: {ModelState.IsValid}");

                if (!ModelState.IsValid)
                {
                    Console.WriteLine("ModelState is invalid:");
                    foreach (var modelState in ModelState)
                    {
                        foreach (var error in modelState.Value.Errors)
                        {
                            Console.WriteLine($"Field '{modelState.Key}': {error.ErrorMessage}");
                        }
                    }

                    // Get cart items for view when validation fails
                    var cartForView = await _context.Carts
                        .Include(c => c.Items)
                        .FirstOrDefaultAsync(c => c.UserId == User.Identity.Name);
                    ViewBag.CartItems = cartForView?.Items?.ToList() ?? new List<CartItem>();

                    return View(order);
                }

                // Lấy thông tin người dùng hiện tại
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    Console.WriteLine("User not authenticated");
                    TempData["ErrorMessage"] = "Bạn cần đăng nhập để thực hiện thanh toán.";
                    return RedirectToAction("Login", "Account");
                }

                Console.WriteLine($"User found: {currentUser.Id} - {currentUser.Email}");

                // Lấy thông tin giỏ hàng (Cart sử dụng email làm UserId)
                Console.WriteLine("Getting cart...");
                var cart = await _context.Carts
                    .Include(c => c.Items)
                    .FirstOrDefaultAsync(c => c.UserId == currentUser.Email);

                if (cart == null || !cart.Items.Any())
                {
                    Console.WriteLine("Cart is empty or null");
                    TempData["ErrorMessage"] = "Giỏ hàng của bạn đang trống. Vui lòng thêm sản phẩm vào giỏ hàng trước khi thanh toán.";
                    return RedirectToAction("Index", "Home");
                }

                Console.WriteLine($"Cart found with {cart.Items.Count} items");

                // Tạo đơn hàng mới
                Console.WriteLine("Creating new order...");
                var totalPrice = cart.Items.Sum(i => i.Price * i.Quantity);
                Console.WriteLine($"Total price calculated: {totalPrice}");

                var newOrder = new Order
                {
                    UserId = currentUser.Id, // Sử dụng ID thực của người dùng
                    OrderDate = DateTime.Now,
                    TotalPrice = totalPrice,
                    Status = OrderStatus.Pending,
                    ShippingAddress = order.ShippingAddress,
                    Notes = order.Notes,
                    PaymentMethod = order.PaymentMethod
                };

                Console.WriteLine("Adding order to context...");
                _context.Orders.Add(newOrder);

                Console.WriteLine("Saving order to database...");
                await _context.SaveChangesAsync();
                Console.WriteLine($"Order saved with ID: {newOrder.Id}");

                // Tạo chi tiết đơn hàng
                Console.WriteLine("Creating order details...");
                foreach (var item in cart.Items)
                {
                    Console.WriteLine($"Processing item: {item.ProductName} - Quantity: {item.Quantity}");

                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        // Kiểm tra số lượng
                        if (product.Quantity < item.Quantity)
                        {
                            Console.WriteLine($"Insufficient stock for {item.ProductName}");
                            TempData["ErrorMessage"] = $"Sản phẩm '{item.ProductName}' chỉ còn {product.Quantity} sản phẩm trong kho.";
                            _context.Orders.Remove(newOrder);
                            await _context.SaveChangesAsync();

                            // Get cart items for view when stock is insufficient
                            ViewBag.CartItems = cart.Items.ToList();
                            return View(order);
                        }

                        // Tạo chi tiết đơn hàng
                        var orderDetail = new OrderDetail
                        {
                            OrderId = newOrder.Id,
                            ProductId = item.ProductId,
                            Quantity = item.Quantity,
                            Price = item.Price,
                            Size = item.Size
                        };

                        Console.WriteLine($"Adding order detail for product {item.ProductId}");
                        _context.OrderDetails.Add(orderDetail);

                        // Cập nhật số lượng sản phẩm
                        Console.WriteLine($"Updating product quantity: {product.Quantity} - {item.Quantity} = {product.Quantity - item.Quantity}");
                        product.Quantity -= item.Quantity;
                        _context.Products.Update(product);
                    }
                    else
                    {
                        Console.WriteLine($"Product not found: {item.ProductId}");
                    }
                }

                // Xóa giỏ hàng
                Console.WriteLine("Removing cart items...");
                _context.CartItems.RemoveRange(cart.Items);
                _context.Carts.Remove(cart);

                // Lưu thay đổi
                Console.WriteLine("Saving final changes...");
                await _context.SaveChangesAsync();

                Console.WriteLine($"=== CHECKOUT SUCCESS - Redirecting to OrderCompleted with ID: {newOrder.Id} ===");

                // Temporary success message instead of redirect to debug
                TempData["SuccessMessage"] = $"Đặt hàng thành công! Mã đơn hàng: #{newOrder.Id}";
                return RedirectToAction("OrderCompleted", new { id = newOrder.Id });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"=== CHECKOUT ERROR ===");
                Console.WriteLine($"Error in Checkout: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Console.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                }

                TempData["ErrorMessage"] = "Đã xảy ra lỗi khi xử lý đơn hàng của bạn. Vui lòng thử lại.";
                return RedirectToAction("Index");
            }
        }

        public async Task<IActionResult> OrderCompleted(int id)
        {
            try
            {
                Console.WriteLine($"=== ORDER COMPLETED START - Order ID: {id} ===");

                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    Console.WriteLine("OrderCompleted - User not found");
                    return RedirectToAction("Login", "Account");
                }

                Console.WriteLine($"OrderCompleted - User: {user.Id} - {user.Email}");

                var order = await _context.Orders
                    .Include(o => o.OrderDetails)
                    .ThenInclude(od => od.Product)
                    .FirstOrDefaultAsync(o => o.Id == id && o.UserId == user.Id);

                Console.WriteLine($"OrderCompleted - Order found: {order != null}");
                if (order != null)
                {
                    Console.WriteLine($"OrderCompleted - Order UserId: {order.UserId}");
                    Console.WriteLine($"OrderCompleted - Current User Id: {user.Id}");
                    Console.WriteLine($"OrderCompleted - Order Details count: {order.OrderDetails?.Count ?? 0}");
                }

                if (order == null)
                {
                    Console.WriteLine("OrderCompleted - Order not found or user mismatch");
                    TempData["ErrorMessage"] = "Không tìm thấy đơn hàng.";
                    return RedirectToAction("Index", "Home");
                }

                Console.WriteLine("OrderCompleted - Success, returning view");
                return View(order);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"=== ORDER COMPLETED ERROR ===");
                Console.WriteLine($"Error in OrderCompleted: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                TempData["ErrorMessage"] = "Đã xảy ra lỗi khi tải thông tin đơn hàng.";
                return RedirectToAction("Index", "Home");
            }
        }

        public async Task<IActionResult> GetOrderSummary()
        {
            // Kiểm tra nếu đây là một yêu cầu AJAX
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                var userId = User.Identity?.Name;
                if (string.IsNullOrEmpty(userId))
                {
                    return PartialView("_OrderSummaryPartial", new List<CartItem>());
                }

                var cart = await _context.Carts
                    .Include(c => c.Items)
                    .FirstOrDefaultAsync(c => c.UserId == userId);

                if (cart == null || cart.Items.Count == 0)
                {
                    return PartialView("_OrderSummaryPartial", new List<CartItem>());
                }

                return PartialView("_OrderSummaryPartial", cart.Items);
            }
            else
            {
                // Nếu đây là một yêu cầu trực tiếp từ trình duyệt, chuyển hướng đến trang MyOrders
                return RedirectToAction(nameof(MyOrders));
            }
        }

        [Authorize]
        public async Task<IActionResult> MyOrders()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return RedirectToAction("Login", "Account");
                }

                var orders = await _context.Orders
                    .Include(o => o.OrderDetails)
                    .ThenInclude(od => od.Product)
                    .Where(o => o.UserId == user.Id)
                    .OrderByDescending(o => o.OrderDate)
                    .ToListAsync();

                if (orders == null || orders.Count == 0)
                {
                    ViewBag.Message = "Bạn chưa có đơn hàng nào.";
                    return View(new List<Order>());
                }

                return View(orders);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in MyOrders: {ex.Message}");
                TempData["ErrorMessage"] = "Đã xảy ra lỗi khi tải danh sách đơn hàng của bạn.";
                return View(new List<Order>());
            }
        }

        [Authorize]
        public async Task<IActionResult> OrderDetails(int id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return RedirectToAction("Login", "Account");
                }

                var order = await _context.Orders
                    .Include(o => o.OrderDetails)
                    .ThenInclude(od => od.Product)
                    .FirstOrDefaultAsync(o => o.Id == id && o.UserId == user.Id);

                if (order == null)
                {
                    TempData["ErrorMessage"] = "Không tìm thấy đơn hàng hoặc bạn không có quyền xem đơn hàng này.";
                    return RedirectToAction(nameof(MyOrders));
                }

                return View(order);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OrderDetails: {ex.Message}");
                TempData["ErrorMessage"] = "Đã xảy ra lỗi khi tải chi tiết đơn hàng của bạn.";
                return RedirectToAction(nameof(MyOrders));
            }
        }

        // Test action để debug
        public async Task<IActionResult> TestCheckout()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                Console.WriteLine($"Test - User: {currentUser?.Id} - {currentUser?.Email}");

                if (currentUser == null)
                {
                    return Json(new { success = false, message = "User not authenticated" });
                }

                var cart = await _context.Carts
                    .Include(c => c.Items)
                    .FirstOrDefaultAsync(c => c.UserId == currentUser.Email);

                Console.WriteLine($"Test - Cart found: {cart != null}");
                Console.WriteLine($"Test - Cart items count: {cart?.Items?.Count ?? 0}");

                if (cart == null || !cart.Items.Any())
                {
                    return Json(new { success = false, message = "Cart is empty" });
                }

                // Test tạo order đơn giản
                var testOrder = new Order
                {
                    UserId = currentUser.Id,
                    OrderDate = DateTime.Now,
                    TotalPrice = 100000,
                    Status = OrderStatus.Pending,
                    ShippingAddress = "Test Address",
                    Notes = "Test Notes",
                    PaymentMethod = PaymentMethod.CashOnDelivery
                };

                Console.WriteLine("Test - Adding order to context...");
                _context.Orders.Add(testOrder);

                Console.WriteLine("Test - Saving to database...");
                await _context.SaveChangesAsync();

                Console.WriteLine($"Test - Order saved with ID: {testOrder.Id}");

                return Json(new { success = true, orderId = testOrder.Id, message = "Test order created successfully" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test Error: {ex.Message}");
                Console.WriteLine($"Test Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Test Inner exception: {ex.InnerException.Message}");
                }
                return Json(new { success = false, message = ex.Message });
            }
        }

        // Test method để kiểm tra OrderCompleted
        public async Task<IActionResult> TestOrderCompleted()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { success = false, message = "User not authenticated" });
                }

                // Tìm order gần nhất của user
                var latestOrder = await _context.Orders
                    .Where(o => o.UserId == currentUser.Id)
                    .OrderByDescending(o => o.OrderDate)
                    .FirstOrDefaultAsync();

                if (latestOrder == null)
                {
                    return Json(new { success = false, message = "No orders found for user" });
                }

                return Json(new {
                    success = true,
                    orderId = latestOrder.Id,
                    userId = currentUser.Id,
                    orderUserId = latestOrder.UserId,
                    message = "Latest order found",
                    redirectUrl = $"/ShoppingCart/OrderCompleted/{latestOrder.Id}"
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
}
