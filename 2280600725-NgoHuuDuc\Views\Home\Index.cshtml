﻿@using NgoHuuDuc_2280600725.Models
@{
    ViewData["Title"] = "Home Page";
    var productsByCategory = ViewBag.ProductsByCategory as Dictionary<string, List<Product>>;
    var categories = ViewBag.Categories as List<Category>;
}

@section Scripts {
    @await Html.PartialAsync("_ThreeJSScriptsPartial")

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all product carousels
            const productCarousels = document.querySelectorAll('.product-carousel .carousel');
            productCarousels.forEach(carousel => {
                // Enable touch swipe on mobile but disable auto sliding
                const carouselInstance = new bootstrap.Carousel(carousel, {
                    interval: false, // Disable auto sliding
                    pause: 'hover', // Pause on hover
                    wrap: true, // Allow wrapping around from last to first slide
                    keyboard: true, // Allow keyboard navigation
                    touch: true // Enable touch swiping on mobile
                });

                // Ensure carousel doesn't start automatically
                carouselInstance.pause();
            });

            // Add hover effect to carousel controls
            const carouselControls = document.querySelectorAll('.product-carousel .carousel-control-prev, .product-carousel .carousel-control-next');
            carouselControls.forEach(control => {
                control.addEventListener('mouseenter', function() {
                    this.style.opacity = '1';
                    this.style.backgroundColor = 'var(--elegant-gold-dark)';
                });

                control.addEventListener('mouseleave', function() {
                    this.style.opacity = '0.8';
                    this.style.backgroundColor = 'var(--elegant-gold)';
                });
            });

            // Make carousel controls more visible on mobile
            function updateCarouselControlsVisibility() {
                const isMobile = window.innerWidth < 768;
                carouselControls.forEach(control => {
                    if (isMobile) {
                        control.style.width = '30px';
                        control.style.height = '30px';
                        control.style.opacity = '0.9';
                    } else {
                        control.style.width = '40px';
                        control.style.height = '40px';
                        control.style.opacity = '0.8';
                    }
                });
            }

            // Initial call
            updateCarouselControlsVisibility();

            // Update on window resize
            window.addEventListener('resize', updateCarouselControlsVisibility);

            // Initialize main carousel
            const mainCarousel = document.getElementById('myCarousel');
            if (mainCarousel) {
                const mainCarouselInstance = new bootstrap.Carousel(mainCarousel, {
                    interval: false, // Disable auto sliding
                    pause: 'hover', // Pause on hover
                    keyboard: true, // Allow keyboard navigation
                    touch: true // Enable touch swiping on mobile
                });

                // Ensure main carousel doesn't start automatically
                mainCarouselInstance.pause();

                // Function to force reflow (needed to restart animation)
                function forceReflow(element) {
                    // This will force a reflow by accessing the computed style
                    void element.offsetWidth;
                }

                // Create a function to handle the slide event for main carousel
                const handleMainSlideEvent = function(event) {
                    // Add animation to indicators
                    const indicators = mainCarousel.querySelectorAll('.carousel-indicators button');
                    const activeIndicator = mainCarousel.querySelector('.carousel-indicators button.active');

                    if (activeIndicator) {
                        // Remove any existing animation classes
                        activeIndicator.classList.remove('carousel-scale-up');

                        // Force a reflow to ensure animation restarts
                        forceReflow(activeIndicator);

                        // Add animation class
                        activeIndicator.classList.add('carousel-scale-up');

                        // Remove the animation class after it completes
                        setTimeout(() => {
                            activeIndicator.classList.remove('carousel-scale-up');
                        }, 300);
                    }

                    // Add animation to the carousel itself
                    mainCarousel.classList.remove('carousel-scale-down');
                    forceReflow(mainCarousel);
                    mainCarousel.classList.add('carousel-scale-down');

                    // Remove the animation class after it completes
                    setTimeout(() => {
                        mainCarousel.classList.remove('carousel-scale-down');
                    }, 300);
                };

                // Create a function to handle the slid event for main carousel
                const handleMainSlidEvent = function(event) {
                    // Add a subtle animation to the active slide
                    const activeSlide = mainCarousel.querySelector('.carousel-item.active');
                    if (activeSlide) {
                        // Remove any existing animation classes
                        activeSlide.classList.remove('carousel-item-zoom-in');

                        // Force a reflow to ensure animation restarts
                        forceReflow(activeSlide);

                        // Add animation class
                        activeSlide.classList.add('carousel-item-zoom-in');

                        // Remove the animation class after it completes
                        setTimeout(() => {
                            activeSlide.classList.remove('carousel-item-zoom-in');
                        }, 500);
                    }
                };

                // Add event listeners using jQuery to ensure proper event handling
                $(mainCarousel).off('slide.bs.carousel').on('slide.bs.carousel', handleMainSlideEvent);
                $(mainCarousel).off('slid.bs.carousel').on('slid.bs.carousel', handleMainSlidEvent);

                // Add animation to main carousel control buttons
                const mainPrevButton = mainCarousel.querySelector('.carousel-control-prev');
                const mainNextButton = mainCarousel.querySelector('.carousel-control-next');

                if (mainPrevButton) {
                    mainPrevButton.addEventListener('click', function() {
                        const icon = this.querySelector('.carousel-control-prev-icon');
                        if (icon) {
                            icon.classList.remove('carousel-control-pulse');
                            forceReflow(icon);
                            icon.classList.add('carousel-control-pulse');
                            setTimeout(() => {
                                icon.classList.remove('carousel-control-pulse');
                            }, 300);
                        }
                    });
                }

                if (mainNextButton) {
                    mainNextButton.addEventListener('click', function() {
                        const icon = this.querySelector('.carousel-control-next-icon');
                        if (icon) {
                            icon.classList.remove('carousel-control-pulse');
                            forceReflow(icon);
                            icon.classList.add('carousel-control-pulse');
                            setTimeout(() => {
                                icon.classList.remove('carousel-control-pulse');
                            }, 300);
                        }
                    });
                }
            }

            // Add visual feedback when user interacts with carousel
            productCarousels.forEach(carousel => {
                // Function to force reflow (needed to restart animation)
                function forceReflow(element) {
                    // This will force a reflow by accessing the computed style
                    void element.offsetWidth;
                }

                // Create a function to handle the slide event
                const handleSlideEvent = function(event) {
                    // Get the carousel element
                    const carouselEl = event.target;

                    // Remove any existing animation classes
                    carouselEl.classList.remove('carousel-scale-down', 'carousel-scale-up');

                    // Force a reflow to ensure animation restarts
                    forceReflow(carouselEl);

                    // Add the scale down animation class
                    carouselEl.classList.add('carousel-scale-down');

                    // Remove the animation class after it completes
                    setTimeout(() => {
                        carouselEl.classList.remove('carousel-scale-down');
                    }, 300);
                };

                // Create a function to handle the slid event
                const handleSlidEvent = function(event) {
                    // Get the carousel element
                    const carouselEl = event.target;

                    // Highlight the active slide
                    const activeSlide = carouselEl.querySelector('.carousel-item.active');
                    if (activeSlide) {
                        // Remove any existing animation classes
                        activeSlide.classList.remove('carousel-item-zoom-in');

                        // Force a reflow to ensure animation restarts
                        forceReflow(activeSlide);

                        // Add the zoom in animation class
                        activeSlide.classList.add('carousel-item-zoom-in');

                        // Remove the animation class after it completes
                        setTimeout(() => {
                            activeSlide.classList.remove('carousel-item-zoom-in');
                        }, 500);
                    }
                };

                // Add event listeners using jQuery to ensure proper event handling
                $(carousel).off('slide.bs.carousel').on('slide.bs.carousel', handleSlideEvent);
                $(carousel).off('slid.bs.carousel').on('slid.bs.carousel', handleSlidEvent);

                // Add animation to control buttons
                const prevButton = carousel.querySelector('.carousel-control-prev');
                const nextButton = carousel.querySelector('.carousel-control-next');

                if (prevButton) {
                    prevButton.addEventListener('click', function() {
                        const icon = this.querySelector('.carousel-control-prev-icon');
                        if (icon) {
                            icon.classList.remove('carousel-control-pulse');
                            forceReflow(icon);
                            icon.classList.add('carousel-control-pulse');
                            setTimeout(() => {
                                icon.classList.remove('carousel-control-pulse');
                            }, 300);
                        }
                    });
                }

                if (nextButton) {
                    nextButton.addEventListener('click', function() {
                        const icon = this.querySelector('.carousel-control-next-icon');
                        if (icon) {
                            icon.classList.remove('carousel-control-pulse');
                            forceReflow(icon);
                            icon.classList.add('carousel-control-pulse');
                            setTimeout(() => {
                                icon.classList.remove('carousel-control-pulse');
                            }, 300);
                        }
                    });
                }
            });
        });
    </script>
}

<div id="myCarousel" class="carousel slide mb-6 carousel-elegant" data-bs-interval="false">
    <div class="carousel-indicators">
        <button type="button" data-bs-target="#myCarousel" data-bs-slide-to="0" class="active" aria-label="Slide 1" aria-current="true"></button>
        <button type="button" data-bs-target="#myCarousel" data-bs-slide-to="1" aria-label="Slide 2" class=""></button>
        <button type="button" data-bs-target="#myCarousel" data-bs-slide-to="2" aria-label="Slide 3" class=""></button>
    </div>
    <div class="carousel-inner">
        <div class="carousel-item active">
            <img src="~/images/img/wall.jpg" alt="Wall" style="width: 100%; height: 750px; object-fit: cover;">
            <div class="container">
                <div class="carousel-caption text-start">
                    <h1>Cửa hàng áo Vest.</h1>
                    <p class="opacity-75">Tổng hợp nhiều loại suit phù hợp cho từng mục đích của bạn.</p>
                    <p><a class="btn btn-lg btn-elegant-primary" asp-area="" asp-controller="Product" asp-action="Index">Sản phẩm</a></p>
                </div>
            </div>
        </div>
        <div class="carousel-item">
            <img src="~/images/img/wall4.jpg" alt="Wall2" style="width: 100%; height: 750px; object-fit: cover;">
            <div class="container">
                <div class="carousel-caption">
                    <h1>Nhận đặt may.</h1>
                    <p>Lựa chọn kiểu dáng, loại vải, màu sắc phù hợp với mục đích của bạn.</p>
                    <p><a class="btn btn-lg btn-elegant-primary" asp-area="" asp-controller="Home" asp-action="Contact">Liên hệ với chúng tôi</a></p>
                </div>
            </div>
        </div>
        <div class="carousel-item">
            <img src="~/images/img/wall3.jpg" alt="Wall2" style="width: 100%; height: 750px; object-fit: cover;">
            <div class="container">
                <div class="carousel-caption text-end">
                    <h1>Cho thuê.</h1>
                    <p>Bạn muốn tiết kiệm chi phí, đến với chúng tôi.</p>
                    <p><a class="btn btn-lg btn-elegant-primary" href="#">Dịch vụ cho thuê</a></p>
                </div>
            </div>
        </div>
    </div>
    <button class="carousel-control-prev" type="button" data-bs-target="#myCarousel" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Previous</span>
    </button>
    <button class="carousel-control-next" type="button" data-bs-target="#myCarousel" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Next</span>
    </button>
</div>

<hr class="featurette-divider">

@* <div class="container mt-4"> *@
@*     <div class="row"> *@
@*         @foreach (var product in Model) *@
@*         { *@
@*             <div class="col-md-4 mb-4"> *@
@*                 <div class="card h-100"> *@
@*                     <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;"> *@
@*                     <div class="card-body"> *@
@*                         <h5 class="card-title">@product.Name</h5> *@
@*                         <p class="card-text">@product.Price.ToString("C")</p> *@
@*                         <form asp-action="AddToCart" asp-controller="Home" method="post"> *@
@*                             <input type="hidden" name="productId" value="@product.Id" /> *@
@*                             <button type="submit" class="btn btn-primary"> *@
@*                                 <i class="bi bi-cart-plus"></i> Add to Cart *@
@*                             </button> *@
@*                         </form> *@
@*                     </div> *@
@*                 </div> *@
@*             </div> *@
@*         } *@
@*     </div> *@
@* </div> *@

@* <hr class="featurette-divider"> *@

<div class="container marketing mt-5">
    <div class="row">
        <!-- Tiêu đề phần sản phẩm nổi bật -->
        <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold text-elegant-gold">Sản phẩm nổi bật</h2>
            <p class="lead">Khám phá bộ sưu tập mới nhất của chúng tôi</p>
            <div class="elegant-divider mb-4"></div>
        </div>

        <!-- Hiển thị sản phẩm theo từng danh mục -->
        @if (productsByCategory != null)
        {
            @foreach (var category in productsByCategory)
            {
                <div class="col-12 mb-5">
                    <h2 class="fw-normal text-center mb-4">@category.Key</h2>
                    <div class="product-carousel-container">
                        <div class="product-carousel">
                            @{
                                var totalProducts = category.Value.Count;
                                var displayedProducts = Math.Min(totalProducts, 10);
                            }

                            <!-- Carousel wrapper -->
                            <div id="<EMAIL>(" ", "")" class="carousel slide" data-bs-interval="false">
                                <!-- Indicators -->
                                <div class="carousel-indicators">
                                    @for (int i = 0; i < (displayedProducts + 3) / 4; i++)
                                    {
                                        <button type="button"
                                                data-bs-target="#<EMAIL>(" ", "")"
                                                data-bs-slide-to="@i"
                                                class="@(i == 0 ? "active" : "")"
                                                aria-current="@(i == 0 ? "true" : "false")"
                                                aria-label="Slide @(i+1)"></button>
                                    }
                                </div>

                                <!-- Carousel items -->
                                <div class="carousel-inner">
                                    @for (int i = 0; i < displayedProducts; i += 4)
                                    {
                                        <div class="carousel-item @(i == 0 ? "active" : "")">
                                            <div class="row">
                                                @for (int j = i; j < Math.Min(i + 4, displayedProducts); j++)
                                                {
                                                    var product = category.Value[j];
                                                    <div class="col-md-3 col-sm-6 col-6">
                                                        <div class="card h-100 product-card">
                                                            <a asp-controller="Product" asp-action="Details" asp-route-id="@product.Id" class="text-decoration-none">
                                                                @if (!string.IsNullOrEmpty(product.ImageUrl))
                                                                {
                                                                    <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 250px; object-fit: cover;">
                                                                }
                                                                else
                                                                {
                                                                    <img src="~/images/no-image.png" class="card-img-top" alt="No Image" style="height: 250px; object-fit: cover;">
                                                                }
                                                                <div class="card-body text-center">
                                                                    <h5 class="card-title">@product.Name</h5>
                                                                    <p class="card-text text-danger fw-bold">@product.Price.ToString("N0") đ</p>
                                                                </div>
                                                            </a>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    }
                                </div>

                                <!-- Controls -->
                                <button class="carousel-control-prev" type="button" data-bs-target="#<EMAIL>(" ", "")" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Previous</span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#<EMAIL>(" ", "")" data-bs-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Next</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        @{
                            var categoryId = categories?.FirstOrDefault(c => c.Name == category.Key)?.Id;
                        }
                        <a asp-controller="Product" asp-action="Index" asp-route-categoryId="@categoryId" class="btn btn-elegant-primary btn-lg">
                            <i class="fas fa-th-list me-2"></i>Xem tất cả sản phẩm @category.Key
                        </a>
                    </div>
                </div>

                <hr class="featurette-divider">
            }
        }

        <!-- Phần mô hình 3D và giới thiệu cửa hàng -->
        <div class="row featurette mb-5">
            <div class="col-md-6">
                <h2 class="featurette-heading fw-normal lh-1">Trải nghiệm mô hình 3D <span class="text-body-secondary">Xem trước sản phẩm chân thực</span></h2>
                <p class="lead">Elegant Suits tự hào giới thiệu công nghệ xem trước sản phẩm 3D, giúp bạn có cái nhìn chân thực nhất về sản phẩm trước khi quyết định mua hàng.</p>
                <p>Với công nghệ hiện đại, bạn có thể:</p>
                <ul>
                    <li>Xoay, phóng to, thu nhỏ để xem chi tiết sản phẩm</li>
                    <li>Thay đổi màu sắc để lựa chọn phù hợp với sở thích</li>
                    <li>Xem trước sản phẩm từ mọi góc độ</li>
                </ul>
                <p><a asp-controller="Product" asp-action="Index" class="btn btn-elegant-primary mt-3">Xem tất cả sản phẩm</a></p>
            </div>
            <div class="col-md-6">
                <div id="model-container" style="height: 400px; border-radius: 10px; overflow: hidden; border: 3px solid var(--elegant-gold-light);" class="shadow"></div>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Hiển thị mô hình 3D cố định từ thư mục models/products
                        show3DModel('model-container', '/models/products/final_suit.glb');
                    });
                </script>
                <div class="model-controls mt-3 text-center">
                    <button class="btn btn-sm btn-elegant-secondary me-2" onclick="changeModel('/models/products/final_suit.glb')">Vest</button>
                    <button class="btn btn-sm btn-elegant-secondary me-2" onclick="changeModel('/models/products/final_shirt.glb')">Áo sơ mi</button>
                    <button class="btn btn-sm btn-elegant-secondary me-2" onclick="changeModel('/models/products/final_trousers.glb')">Quần tây</button>
                    <button class="btn btn-sm btn-elegant-secondary" onclick="changeModel('/models/products/final_gile.glb')">Gile</button>
                </div>
            </div>
        </div>

        <hr class="featurette-divider">

        <div class="row featurette mb-5">
            <div class="col-md-7 order-md-2">
                <h2 class="featurette-heading fw-normal lh-1">Về Elegant Suits <span class="text-body-secondary">Đẳng cấp và phong cách</span></h2>
                <p class="lead">Elegant Suits là thương hiệu thời trang nam cao cấp chuyên cung cấp các sản phẩm vest, quần tây, áo sơ mi với chất lượng hàng đầu.</p>
                <p>Chúng tôi cam kết:</p>
                <ul>
                    <li>Sử dụng chất liệu cao cấp, nhập khẩu từ các thương hiệu nổi tiếng</li>
                    <li>Thiết kế tinh tế, phù hợp với xu hướng thời trang hiện đại</li>
                    <li>Đa dạng mẫu mã, kích cỡ phù hợp với mọi vóc dáng</li>
                    <li>Dịch vụ chăm sóc khách hàng tận tâm</li>
                </ul>
                <p><a href="#" class="btn btn-elegant-secondary mt-3">Tìm hiểu thêm về chúng tôi</a></p>
            </div>
            <div class="col-md-5 order-md-1">
                <img class="bd-placeholder-img bd-placeholder-img-lg featurette-image img-fluid mx-auto rounded-5 shadow" width="500" height="500" src="~/images/img/maydo.jpg" alt="Elegant Suits" style="border: 3px solid var(--elegant-gold-light);">
            </div>
        </div>

        <hr class="featurette-divider">

        <!-- Phần hiển thị thành viên -->
        <h2 class="fw-normal text-center mb-4">Đội ngũ của chúng tôi</h2>
        <div class="row mb-5">
            <div class="col-12 col-sm-6 col-lg-4 text-center">
                <img src="~/images/img/ha.jpg" class="bd-placeholder-img rounded-circle profile-img-elegant" width="140" height="140" alt="Peple1">
                <h2 class="fw-normal">Hoàng Ân</h2>
                <p>MSSV: 2280600149</p>
                <p><a class="btn btn-elegant-secondary" href="https://github.com/SushiRedpanda" target="_blank">Xem thêm »</a></p>
            </div>
            <div class="col-12 col-sm-6 col-lg-4 text-center">
                <img src="~/images/img/hd.jpg" class="bd-placeholder-img rounded-circle profile-img-elegant" width="140" height="140" alt="Peple2">
                <h2 class="fw-normal">Ngô Hữu Đức</h2>
                <p>MSSV: 2280600725</p>
                <p><a class="btn btn-elegant-secondary" href="https://github.com/khoangrtroongs" target="_blank">Xem thêm »</a></p>
            </div>
            <div class="col-12 col-sm-6 col-lg-4 text-center">
                <img src="~/images/img/qt.jpg" class="bd-placeholder-img rounded-circle profile-img-elegant" width="140" height="140" alt="Peple3">
                <h2 class="fw-normal">Nguyễn Quốc Trung</h2>
                <p>MSSV: 2280600357</p>
                <p><a class="btn btn-elegant-secondary" href="https://github.com/TrungQNdev20" target="_blank">Xem thêm »</a></p>
            </div>
        </div>
    </div>
</div>
