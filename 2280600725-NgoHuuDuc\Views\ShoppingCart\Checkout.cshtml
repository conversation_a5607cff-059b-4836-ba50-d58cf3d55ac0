@model Order
@using Microsoft.EntityFrameworkCore
@using NgoHuuDuc_2280600725.Data
@using NgoHuuDuc_2280600725.Models
@inject ApplicationDbContext DbContext

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
        <li class="breadcrumb-item"><a asp-controller="ShoppingCart" asp-action="Index">Giỏ hàng</a></li>
        <li class="breadcrumb-item active" aria-current="page">Thanh toán</li>
    </ol>
</nav>

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="row">
    <div class="col-lg-8">
        <div class="card card-elegant">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Thông tin khách hàng</h5>
            </div>
            <div class="card-body">
                <form asp-action="Checkout" method="post" id="checkoutForm">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                    <!-- Customer Information Display (Read-only) -->
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">Thông tin khách hàng</label>
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">Tên đăng nhập:</small>
                                            <div class="fw-bold">@User.Identity.Name</div>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="text-muted">Thời gian đặt hàng:</small>
                                            <div class="fw-bold">@DateTime.Now.ToString("dd/MM/yyyy HH:mm")</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12 mb-3">
                            <label for="ShippingAddress" class="form-label">Địa chỉ giao hàng <span class="text-danger">*</span></label>
                            <textarea class="form-control"
                                      id="ShippingAddress"
                                      name="ShippingAddress"
                                      asp-for="ShippingAddress"
                                      rows="3"
                                      required
                                      minlength="10"
                                      placeholder="Nhập địa chỉ chi tiết để giao hàng (số nhà, tên đường, phường/xã, quận/huyện, tỉnh/thành phố)"></textarea>
                            <div class="invalid-feedback">
                                Vui lòng nhập địa chỉ giao hàng (ít nhất 10 ký tự).
                            </div>
                            <span asp-validation-for="ShippingAddress" class="text-danger"></span>
                        </div>

                        <div class="col-md-12 mb-3">
                            <label for="Notes" class="form-label">Ghi chú đơn hàng (tùy chọn)</label>
                            <textarea class="form-control"
                                      id="Notes"
                                      name="Notes"
                                      asp-for="Notes"
                                      rows="2"
                                      placeholder="Ghi chú thêm về đơn hàng (thời gian giao hàng, yêu cầu đặc biệt...)"></textarea>
                        </div>

                        <!-- Payment Method Selection -->
                        <div class="col-md-12 mb-3">
                            <label class="form-label">Phương thức thanh toán</label>
                            <div class="payment-methods">
                                <div class="form-check payment-option">
                                    <input class="form-check-input"
                                           type="radio"
                                           name="PaymentMethod"
                                           id="cod"
                                           value="0"
                                           checked>
                                    <label class="form-check-label payment-label" for="cod">
                                        <div class="payment-icon">
                                            <i class="fas fa-truck"></i>
                                        </div>
                                        <div class="payment-info">
                                            <strong>Thanh toán khi nhận hàng (COD)</strong>
                                            <small class="text-muted d-block">Thanh toán bằng tiền mặt khi nhận hàng</small>
                                        </div>
                                    </label>
                                </div>
                                <div class="form-check payment-option">
                                    <input class="form-check-input"
                                           type="radio"
                                           name="PaymentMethod"
                                           id="online"
                                           value="1">
                                    <label class="form-check-label payment-label" for="online">
                                        <div class="payment-icon">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <div class="payment-info">
                                            <strong>Thanh toán online</strong>
                                            <small class="text-muted d-block">Thanh toán qua thẻ tín dụng, ví điện tử</small>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-controller="ShoppingCart" asp-action="Index" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại giỏ hàng
                        </a>
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-check me-2"></i>Xác nhận đặt hàng
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card card-elegant">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Tóm tắt đơn hàng</h5>
            </div>
            <div class="card-body">
                @{
                    var userId = User.Identity?.Name;
                    var cart = await DbContext.Carts
                        .Include(c => c.Items)
                        .FirstOrDefaultAsync(c => c.UserId == userId);

                    if (cart != null && cart.Items.Any())
                    {
                        <!-- Danh sách sản phẩm -->
                        <div class="mb-3">
                            @foreach (var item in cart.Items)
                            {
                                <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                                    <div class="d-flex align-items-center">
                                        @if (!string.IsNullOrEmpty(item.ImageUrl))
                                        {
                                            <img src="@item.ImageUrl"
                                                 alt="@item.ProductName"
                                                 class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                        }
                                        else
                                        {
                                            <img src="https://via.placeholder.com/40x40?text=No+Image"
                                                 alt="No Image" class="img-thumbnail me-2" style="width: 40px; height: 40px;">
                                        }
                                        <div>
                                            <h6 class="mb-0 small">@item.ProductName</h6>
                                            <small class="text-muted">SL: @item.Quantity</small>
                                        </div>
                                    </div>
                                    <span class="fw-bold small">
                                        @((item.Price * item.Quantity).ToString("C0"))
                                    </span>
                                </div>
                            }
                        </div>

                        <!-- Tính toán -->
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tạm tính:</span>
                            <span class="fw-bold">
                                @cart.Items.Sum(i => i.Price * i.Quantity).ToString("C0")
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Phí vận chuyển:</span>
                            <span class="text-success fw-bold">Miễn phí</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Thuế VAT:</span>
                            <span class="text-muted">Đã bao gồm</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="h5">Tổng cộng:</span>
                            <span class="h5 text-primary fw-bold">
                                @cart.Items.Sum(i => i.Price * i.Quantity).ToString("C0")
                            </span>
                        </div>

                        <!-- Phương thức thanh toán -->
                        <div class="alert alert-info">
                            <h6 class="alert-heading"><i class="fas fa-credit-card me-2"></i>Phương thức thanh toán</h6>
                            <p class="mb-0 small">Thanh toán khi nhận hàng (COD)</p>
                        </div>

                        <!-- Chính sách -->
                        <div class="small text-muted">
                            <p class="mb-1"><i class="fas fa-shield-alt me-1"></i> Bảo hành chính hãng</p>
                            <p class="mb-1"><i class="fas fa-truck me-1"></i> Giao hàng toàn quốc</p>
                            <p class="mb-0"><i class="fas fa-undo me-1"></i> Đổi trả trong 7 ngày</p>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            Giỏ hàng của bạn đang trống. Vui lòng thêm sản phẩm vào giỏ hàng trước khi thanh toán.
                        </div>
                    }
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <!-- JavaScript for form validation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('checkoutForm');

            form.addEventListener('submit', function(event) {
                console.log('=== FORM SUBMIT EVENT ===');

                // Get form data
                const formData = new FormData(form);
                console.log('Form data:');
                for (let [key, value] of formData.entries()) {
                    console.log(`${key}: ${value}`);
                }

                if (!form.checkValidity()) {
                    console.log('Form validation failed');
                    event.preventDefault();
                    event.stopPropagation();
                } else {
                    console.log('Form validation passed, submitting...');
                }

                form.classList.add('was-validated');
            });
        });
    </script>
}

<style>
.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.payment-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
    margin-bottom: 0;
}

.payment-option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.payment-option input[type="radio"]:checked + .payment-label {
    color: #007bff;
}

.payment-option input[type="radio"]:checked {
    border-color: #007bff;
}

.payment-option input[type="radio"]:checked ~ * {
    border-color: #007bff;
}

.payment-option:has(input[type="radio"]:checked) {
    border-color: #007bff;
    background-color: #e7f3ff;
}

.payment-label {
    display: flex;
    align-items: center;
    gap: 15px;
    cursor: pointer;
    margin-bottom: 0;
    width: 100%;
}

.payment-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border-radius: 50%;
    color: #6c757d;
    font-size: 18px;
}

.payment-option:has(input[type="radio"]:checked) .payment-icon {
    background-color: #007bff;
    color: white;
}

.payment-info {
    flex: 1;
}

.payment-info strong {
    display: block;
    margin-bottom: 4px;
}

.form-check-input {
    margin-top: 0;
    margin-right: 10px;
}
</style>
