@model List<CartItem>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Giỏ hàng</li>
    </ol>
</nav>

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="row">
    <div class="col-lg-8">
        <div class="card card-elegant">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Giỏ hàng của bạn</h5>
            </div>
            <div class="card-body">
                @if (!Model.Any())
                {
                    <!-- Giỏ hàng rỗng -->
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">Giỏ hàng của bạn đang trống</h4>
                        <p class="text-muted">Hãy thêm một số sản phẩm vào giỏ hàng để tiếp tục mua sắm.</p>
                        <a asp-controller="Product" asp-action="Index" class="btn btn-elegant-primary">
                            <i class="fas fa-arrow-left me-2"></i>Tiếp tục mua sắm
                        </a>
                    </div>
                }
                else
                {
                    <!-- Bảng sản phẩm -->
                    <form id="cartForm">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Hình ảnh</th>
                                        <th>Tên sản phẩm</th>
                                        <th>Giá</th>
                                        <th>Số lượng</th>
                                        <th>Thành tiền</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model)
                                    {
                                        <tr>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.ImageUrl))
                                                {
                                                    <img src="@item.ImageUrl"
                                                         alt="@item.ProductName"
                                                         class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                }
                                                else
                                                {
                                                    <img src="https://via.placeholder.com/60x60?text=No+Image"
                                                         alt="No Image" class="img-thumbnail" style="width: 60px; height: 60px;">
                                                }
                                            </td>
                                            <td>
                                                <h6 class="mb-0">@item.ProductName</h6>
                                            </td>
                                            <td>
                                                <span class="text-primary fw-bold">
                                                    @item.Price.ToString("C0")
                                                </span>
                                            </td>
                                            <td>
                                                <div class="input-group" style="width: 120px;">
                                                    <input type="number"
                                                           value="@item.Quantity"
                                                           min="1"
                                                           max="99"
                                                           class="form-control text-center quantity-input"
                                                           data-price="@item.Price"
                                                           data-product-id="@item.ProductId">
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold subtotal" data-product-id="@item.ProductId">
                                                    @((item.Price * item.Quantity).ToString("C0"))
                                                </span>
                                            </td>
                                            <td>
                                                <form asp-controller="Home" asp-action="RemoveFromCart" method="post" class="d-inline remove-from-cart-form">
                                                    <input type="hidden" name="productId" value="@item.ProductId" />
                                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <a asp-controller="Product" asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Tiếp tục mua sắm
                            </a>
                            <button type="button" class="btn btn-elegant-primary" onclick="updateCart()">
                                <i class="fas fa-sync-alt me-2"></i>Cập nhật giỏ hàng
                            </button>
                        </div>
                    </form>
                }
            </div>
        </div>
    @if (Model.Any())
    {
        <div class="col-lg-4">
            <div class="card card-elegant">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Tóm tắt đơn hàng</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Số lượng sản phẩm:</span>
                        <span class="fw-bold" id="totalItems">
                            @Model.Sum(i => i.Quantity)
                        </span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tạm tính:</span>
                        <span class="fw-bold" id="subtotalAmount">
                            @Model.Sum(i => i.Price * i.Quantity).ToString("C0")
                        </span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Phí vận chuyển:</span>
                        <span class="text-success fw-bold">Miễn phí</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <span class="h5">Tổng cộng:</span>
                        <span class="h5 text-primary fw-bold" id="totalAmount">
                            @Model.Sum(i => i.Price * i.Quantity).ToString("C0")
                        </span>
                    </div>
                    <a asp-controller="ShoppingCart" asp-action="Checkout" class="btn btn-success w-100 btn-lg">
                        <i class="fas fa-credit-card me-2"></i>Tiến hành thanh toán
                    </a>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Remove from cart functionality
            $('.remove-from-cart-form').on('submit', function (e) {
                e.preventDefault();
                var form = $(this);

                $.ajax({
                    url: form.attr('action'),
                    method: 'POST',
                    data: form.serialize(),
                    success: function () {
                        location.reload(); // Reload the page after removing item
                        updateCartCount(); // Update cart count
                    },
                    error: function () {
                        alert('Có lỗi xảy ra khi xóa sản phẩm.');
                    }
                });
            });

            // Real-time quantity update
            const quantityInputs = document.querySelectorAll('.quantity-input');

            quantityInputs.forEach(input => {
                input.addEventListener('input', function() {
                    updateSubtotal(this);
                    updateTotal();
                });
            });

            function updateSubtotal(input) {
                const price = parseFloat(input.dataset.price);
                const quantity = parseInt(input.value) || 0;
                const productId = input.dataset.productId;
                const subtotal = price * quantity;

                const subtotalElement = document.querySelector(`.subtotal[data-product-id="${productId}"]`);
                if (subtotalElement) {
                    subtotalElement.textContent = new Intl.NumberFormat('vi-VN', {
                        style: 'currency',
                        currency: 'VND',
                        minimumFractionDigits: 0
                    }).format(subtotal);
                }
            }

            function updateTotal() {
                let total = 0;
                let totalItems = 0;

                quantityInputs.forEach(input => {
                    const price = parseFloat(input.dataset.price);
                    const quantity = parseInt(input.value) || 0;
                    total += price * quantity;
                    totalItems += quantity;
                });

                document.getElementById('totalItems').textContent = totalItems;
                const formattedTotal = new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND',
                    minimumFractionDigits: 0
                }).format(total);

                document.getElementById('subtotalAmount').textContent = formattedTotal;
                document.getElementById('totalAmount').textContent = formattedTotal;
            }

            // Update cart function for the update button
            window.updateCart = function() {
                // Collect all quantity changes
                const updates = [];
                quantityInputs.forEach(input => {
                    updates.push({
                        productId: input.dataset.productId,
                        quantity: parseInt(input.value)
                    });
                });

                // Send updates to server
                $.ajax({
                    url: '/Home/UpdateCart',
                    method: 'POST',
                    data: { updates: updates },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Có lỗi xảy ra khi cập nhật giỏ hàng.');
                        }
                    },
                    error: function() {
                        alert('Có lỗi xảy ra khi cập nhật giỏ hàng.');
                    }
                });
            };
        });
    </script>
}
